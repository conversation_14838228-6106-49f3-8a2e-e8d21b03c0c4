import ijson
import json
import uuid
from openai import OpenAI
from pathlib import Path
from typing import Optional, List, Dict, Tuple, Sequence, Any, Union
import nltk
from nltk.util import ngrams
import jieba
from sentence_transformers import SentenceTransformer, util
import numpy as np
import itertools
import random
import yaml
from scipy.spatial.distance import cosine
from functools import lru_cache


# --- Configuration ---
# API Configuration for LLM
# API_KEY = "sk-RMerNTL9uP4lmHh_1rwUHJvcaqKXXzb7IrAtjWTa5Ln_aFMFTNPkuzIi7vw"
# BASE_URL = "http://*************:3000/v1"
# LLM_MODEL = "deepseek-r1-250528"  # Or any other model from the list

# File Paths
# PSYQA_FILE = Path("PsyQA_full.json")
OUTPUT_FILE = Path("expand_sim_doubao.json")
# NUM_PERSONAS_TO_GENERATE = 100 # Generate 10 personas for this run

# Add a section for NLTK data download check
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    print("NLTK 'punkt' tokenizer not found. Downloading...")
    nltk.download('punkt', quiet=True)
    print("'punkt' tokenizer downloaded.")
try:
    nltk.data.find('tokenizers/punkt_tab')
except LookupError:
    print("NLTK 'punkt_tab' not found. Downloading...")
    nltk.download('punkt_tab', quiet=True)
    print("'punkt_tab' downloaded.")

def _generate_ngrams(tokens: Sequence[str], n: int) -> List[Tuple[str, ...]]:
    """一个辅助函数，用于从token列表中生成n-grams。"""
    # 使用元组(tuple)来表示n-gram，因为元组是可哈希的，可以放入集合中
    return [tuple(tokens[i:i+n]) for i in range(len(tokens) - n + 1)]

def normalize_field_value(value: Any) -> str:
    """
    将各种类型的字段值标准化为字符串，用于文本分析
    
    Args:
        value: 任意类型的字段值（字符串、列表、字典等）
        
    Returns:
        str: 标准化后的字符串
    """
    if isinstance(value, str):
        return value
    elif isinstance(value, list):
        # 如果是列表，将所有元素转为字符串并用空格连接
        return " ".join(normalize_field_value(item) for item in value if item)
    elif isinstance(value, dict):
        # 如果是字典，将所有值转为字符串并用空格连接
        return " ".join(normalize_field_value(v) for v in value.values() if v)
    else:
        # 其他类型尝试转为字符串
        try:
            return str(value)
        except:
            return ""

def calculate_distinct_n(texts: list[str], n: int) -> Optional[float]:
    """
    计算文本列表中n-gram的多样性得分。
    得分为不同n-gram数量与总n-gram数量的比率。
    
    Args:
        texts (list[str]): 文本列表
        n (int): n-gram的n值
        
    Returns:
        float: distinct-n得分，如果无法计算则返回0.0
    """
    if not texts:
        return 0.0

    all_ngrams = []
    for text in texts:
        tokens = jieba.lcut(text)
        all_ngrams.extend(ngrams(tokens, n))

    if not all_ngrams:
        return 0.0

    return len(set(all_ngrams)) / len(all_ngrams) if all_ngrams else 0.0

def compute_distinct_n(texts: List[str], max_n: int = 3) -> Dict[str, float]:
    """
    计算并返回给定文本列表的 Distinct-N 指标。

    该函数会计算从 1 到 max_n 的所有 n-gram 的多样性分数。
    Distinct-N = (不重复的 N-gram 数量) / (总 N-gram 数量)

    Args:
        texts (List[str]): 一个包含多条文本的列表。
        max_n (int): 需要计算的最大 N 值，默认为 3（即计算 distinct-1, distinct-2和distinct-3）。

    Returns:
        Dict[str, float]: 一个字典，键为 'distinct_n'，值为对应的多样性分数。
                        如果无法生成任何 n-gram（例如文本太短），则返回 0.0。
    """
    if not texts:
        return {f"distinct_{i}": 0.0 for i in range(1, max_n + 1)}

    results = {}
    for n in range(1, max_n + 1):
        total_ngrams = 0
        unique_ngrams = set()

        for text in texts:
            tokens = jieba.lcut(text)
            if len(tokens) < n:
                # 如果某条文本长度小于n，无法生成n-gram
                continue

            # 生成所有的 n- grams
            text_ngrams = list(ngrams(tokens, n))

            total_ngrams += len(text_ngrams)
            unique_ngrams.update(text_ngrams)

        # 计算 Distinct-N 分数
        if total_ngrams > 0:
            diversity_score = len(unique_ngrams) / total_ngrams
        else:
            diversity_score = 0.0
        results[f"distinct_{n}"] = diversity_score

    return results

def calculate_semantic_diversity(texts: list[str]):
    """
    使用句子嵌入计算语义多样性。
    返回平均成对余弦相似度。值越低表示多样性越高。
    
    Args:
        texts (list[str]): 文本列表
        
    Returns:
        float: 平均语义相似度分数，如果无法计算则返回1.0
    """
    if len(texts) < 2:
        return 1.0  # 或 None，因为无法应用多样性

    print("成对相似度方法...")
    try:
        model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        # print("SentenceTransformer模型已加载。")
        
        embeddings = model.encode(texts, convert_to_tensor=True)
        # print("嵌入向量已生成。")

        # 计算所有对的余弦相似度
        cosine_scores = util.cos_sim(embeddings, embeddings)

        # 获取所有唯一对
        pairs = list(itertools.combinations(range(len(texts)), 2))
        
        if not pairs:
            return 1.0

        # 对所有唯一对的相似度分数求和
        total_similarity = sum(cosine_scores[i][j] for i, j in pairs)
        
        # 计算平均相似度
        average_similarity = total_similarity / len(pairs)
        
        return average_similarity.item()

    except Exception as e:
        print(f"无法计算语义多样性。请确保已安装'sentence-transformers'和'torch'（`pip install sentence-transformers`）。")
        print(f"错误: {e}")
        return None

def compute_semantic_dispersion(texts: List[str]) -> float:
    """
    通过计算嵌入空间中的离散度来衡量一组文本的语义多样性。

    该方法执行以下步骤：
    1. 使用SentenceTransformer模型将文本编码为高维向量。
    2. 计算所有向量的质心（平均向量）。
    3. 计算每个向量到质心的平均余弦距离。

    距离越大，表示文本在语义上越分散，即多样性越高。

    Args:
        texts (List[str]): 需要分析的文本列表。

    Returns:
        float: 语义离散度分数。分数越高，多样性越高。
               如果文本少于2条，无法计算离散度，返回 0.0。
    """
    # 边缘情况处理：如果少于2个句子，无法形成"分布"，离散度为0
    if len(texts) < 2:
        return 0.0

    print("质心距离方法...")
    try:
        # 加载模型并编码文本
        model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        # print("SentenceTransformer模型已加载。")
        
        # 将文本列表编码为嵌入向量矩阵
        embeddings = model.encode(texts, show_progress_bar=False)
        # print("嵌入向量已生成。")

        # 计算所有嵌入向量的质心
        centroid = np.mean(embeddings, axis=0)

        # 计算每个向量到质心的余弦距离
        # 余弦距离 = 1 - 余弦相似度。距离越小，语义越接近。
        distances = [cosine(embedding, centroid) for embedding in embeddings]

        # 计算平均距离作为最终的离散度分数
        dispersion_score = float(np.mean(distances))
        
        return dispersion_score
        
    except Exception as e:
        print(f"无法计算语义分散度。请确保已安装'sentence-transformers'和'scipy'。")
        print(f"错误: {e}")
        return None

def main():
    """主函数：读取生成的角色卡片并分析多样性"""
    
    # 读取生成的角色卡片
    with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
        generated_personas = json.load(f)

    # 要分析的字段列表
    fields_to_analyze = ["StrengthsAndResources", "SocialSupportSystem", "FormativeExperiences", "InterestsAndValues"]
    
    # 对每个字段进行多样性分析
    for field in fields_to_analyze:
        print(f"\n'{field}'...")
        
        # 收集所有有效的字段值并标准化处理
        field_texts = []
        for persona in generated_personas:
            value = persona.get(field)
            if value:  # 确保字段存在且不为空
                # 将任何类型的字段值标准化为字符串
                normalized_value = normalize_field_value(value)
                if normalized_value.strip():  # 确保标准化后的文本不为空
                    field_texts.append(normalized_value)
        
        if not field_texts:
            print(f"在数据中找不到'{field}'字段或字段值为空。")
            continue
        
        print(f"找到{len(field_texts)}个有效值。")
        
        # 计算distinct-n (使用新方法)
        # print("\nDistinct-N...")
        dist_results = compute_distinct_n(field_texts, max_n=3)
        
        for n, score in dist_results.items():
            print(f"{n.replace('_', '-')}得分: {score:.4f}")
        
        # 计算并打印语义多样性 (使用原方法 - 成对计算)
        avg_similarity = calculate_semantic_diversity(field_texts)
        if avg_similarity is not None:
            print(f"{avg_similarity:.4f} (值越低表示多样性越高)")
            
        # 计算并打印语义多样性 (使用新方法 - 质心距离)
        dispersion_score = compute_semantic_dispersion(field_texts)
        if dispersion_score is not None:
            print(f"{dispersion_score:.4f} (值越高表示多样性越高)")


if __name__ == "__main__":
    main() 