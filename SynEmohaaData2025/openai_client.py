"""
简单封装openai，提供与`DifySession`类似的接口
"""
import traceback
import dataclasses
from typing import Optional

import openai


@dataclasses.dataclass
class OpenAISessionArguments:
    base_url: str
    api_key: str
    model: str
    decoding_params: Optional[dict] = None
    no_think: bool = False


class OpenAISession:
    def __init__(self, *, base_url: str, api_key: str = "empty", model: str, decoding_params: Optional[dict] = None, no_think: bool = False):
        self.base_url = base_url
        self.api_key = api_key
        self.model = model
        self.client = openai.OpenAI(base_url=base_url, api_key=api_key)
        
        self.decoding_params = decoding_params
        self.no_think = no_think

        self.messages = []
        
    def initialize(self, system: Optional[str] = None):
        self.messages = []
        if system:
            self.messages.append({"role": "system", "content": system})
    
    def chat(self, query: str, decoding_params: Optional[dict] = None, no_think: Optional[bool] = None, return_reasoning_content: bool = False) -> str:
        if decoding_params is None:
            decoding_params = self.decoding_params
        if no_think is None:
            no_think = self.no_think
        
        self.messages.append({"role": "user", "content": query})
        if no_think:
            self.messages[-1]['content'] += "/no_think"
        
        decoding_params = decoding_params or {}
        decoding_params.setdefault("max_tokens", 2048)
        decoding_params.setdefault("temperature", 0.7)
        decoding_params.setdefault("top_p", 0.95)
        
        for _ in range(3):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=self.messages,
                    stream=False,
                    **decoding_params
                )
                # assistant_content = response.choices[0].message.content or response.choices[0].message.reasoning_content
                content = response.choices[0].message.content
                try:
                    reasoning_content = response.choices[0].message.reasoning_content or ""
                except AttributeError:
                    # 非推理模型，没有reasoning_content字段
                    reasoning_content = ""
            except Exception as e:
                traceback.print_tb(e.__traceback__)
                content = None
                reasoning_content = ""

            if content:
                break
        
        if no_think:
            self.messages[-1]['content'] = query
        
        if reasoning_content:
            # api已经将思考过程单独解析出来
            final_reasoning_content = reasoning_content
        elif (not reasoning_content) and "<think>" in content:
            # api没有将思考过程单独解析出来，且content包含思考过程
            # <think>思考</think>回答
            final_reasoning_content = content.split("<think>")[-1].split("</think>")[0]
        else:
            # 非思考模型，无思考过程
            final_reasoning_content = reasoning_content
        
        if (not reasoning_content) and "<think>" in content:
            # api没有将思考过程单独解析出来，且content包含思考过程
            content_wo_think = content.split("</think>")[-1]
        else:
            # api已经将思考过程单独解析出来，或者content不包含思考过程
            content_wo_think = content
        
        self.messages.append({"role": "assistant", "content": content_wo_think.strip()})
        
        if return_reasoning_content:
            return final_reasoning_content, content_wo_think.strip()
        else:
            return content_wo_think.strip()
