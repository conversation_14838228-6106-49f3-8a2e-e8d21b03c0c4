"""
分析对话中，来访者回复的多样性
"""
import json
import os
from pathlib import Path
from functools import lru_cache
from typing import List, Dict, Tuple, Sequence, Literal, Optional

import numpy as np
from scipy.spatial.distance import cosine
from sentence_transformers import SentenceTransformer


def _generate_ngrams(tokens: Sequence[str], n: int) -> List[Tuple[str, ...]]:
    """一个辅助函数，用于从token列表中生成n-grams。"""
    # 使用元组(tuple)来表示n-gram，因为元组是可哈希的，可以放入集合中
    return [tuple(tokens[i:i+n]) for i in range(len(tokens) - n + 1)]

def compute_distinct_n(texts: List[str], max_n: int = 2) -> Dict[str, float]:
    """
    计算并返回给定文本列表的 Distinct-N 指标。

    该函数会计算从 1 到 max_n 的所有 n-gram 的多样性分数。
    Distinct-N = (不重复的 N-gram 数量) / (总 N-gram 数量)

    Args:
        texts (List[str]): 一个包含多条文本（回复）的列表。
        max_n (int): 需要计算的最大 N 值，默认为 2（即计算 distinct-1 和 distinct-2）。

    Returns:
        Dict[str, float]: 一个字典，键为 'distinct_n'，值为对应的多样性分数。
                         如果无法生成任何 n-gram（例如文本太短），则返回 0.0。
    """
    if not texts:
        return {f"distinct_{i}": 0.0 for i in range(1, max_n + 1)}

    
    results = {}
    for n in range(1, max_n + 1):
        total_ngrams = 0
        unique_ngrams = set()
        
        for text in texts:
            if len(text) < n:
                # 如果某条文本长度小于n，无法生成n-gram
                continue
                
            # 生成所有的 n-grams
            ngrams = _generate_ngrams(text, n)
            
            total_ngrams += len(ngrams)
            unique_ngrams.update(ngrams)
        

        
        # 计算 Distinct-N 分数
        if total_ngrams > 0:
            diversity_score = len(unique_ngrams) / total_ngrams
        else:
            diversity_score = 0.0
        results[f"distinct_{n}"] = diversity_score
        
    return results


def compute_semantic_dispersion(
    texts: List[str], 
    model: SentenceTransformer,
) -> float:
    """
    通过计算嵌入空间中的离散度来衡量一组文本的语义多样性。

    该方法执行以下步骤：
    1. 使用SentenceTransformer模型将文本编码为高维向量。
    2. 计算所有向量的质心（平均向量）。
    3. 计算每个向量到质心的平均余弦距离。
    
    距离越大，表示文本在语义上越分散，即多样性越高。

    Args:
        texts (List[str]): 需要分析的文本（回复）列表。
        model (SentenceTransformer, optional): 预先加载的SentenceTransformer模型。
                                             传入此参数可以避免在函数内重复加载模型，提高效率。
                                             默认为 None。

    Returns:
        float: 语义离散度分数。分数越高，多样性越高。
               如果文本少于2条，无法计算离散度，返回 0.0。
    """
    # 边缘情况处理：如果少于2个句子，无法形成“分布”，离散度为0
    if len(texts) < 2:
        return 0.0

    # 步骤1: 加载模型并编码文本
    # 为了效率，推荐在函数外部加载模型并将其作为参数传入
    
    # 将文本列表编码为嵌入向量矩阵
    embeddings = model.encode(texts, show_progress_bar=False)
    
    # 步骤2: 计算所有嵌入向量的质心
    centroid = np.mean(embeddings, axis=0)
    
    # 步骤3: 计算每个向量到质心的余弦距离
    # 余弦距离 = 1 - 余弦相似度。距离越小，语义越接近。
    distances = [cosine(embedding, centroid) for embedding in embeddings]
    
    # 步骤4: 计算平均距离作为最终的离散度分数
    dispersion_score = float(np.mean(distances))
    
    return dispersion_score


def get_length_metrics(texts: List[str]):
    lengths = [len(text) for text in texts]
    return {
        "mean_length": float(np.mean(lengths)),
        "median_length": float(np.median(lengths)),
        "std_length": float(np.std(lengths)),
        "max_length": float(np.max(lengths)),
    }


def read_contents(input_file_or_dir: str, role: Optional[Literal['user', 'assistant']] = None):
    # 选择文件
    if os.path.isfile(input_file_or_dir):
        files = [input_file_or_dir]
    elif os.path.isdir(input_file_or_dir):
        files = list(Path(input_file_or_dir).glob('*.json'))
    else:
        raise FileNotFoundError(f"Input file or directory not found: {input_file_or_dir}")


    # 读取文件，提前role的所有文本
    assert role in (None, "user", "assistant"), "role must be one of 'user', 'assistant' or None"
    texts = []
    for file in files:
        with open(file, 'r', encoding='utf-8') as reader:
            dialogues = json.load(reader)
        
        for dialogue in dialogues:
            for msg in dialogue:
                if role is None or role == msg['role']:
                    texts.append(msg['content'])
        
    return texts


def load_embedding_model():
    import torch
    if torch.cuda.is_available():
        device = 'cuda'
    else:
        device = 'cpu'
    return SentenceTransformer("/data0/hf_models/BAAI/bge-m3/", device=device)


def main(*, input_file_or_dir: str, role: Optional[Literal['user', 'assistant']] = None, output_file: Optional[str] = None):
    texts = read_contents(input_file_or_dir, role)
    
    distinct_4 = compute_distinct_n(texts, max_n=4)
    # semantic_dispersion = compute_semantic_dispersion(texts, load_embedding_model())
    length_metrics = get_length_metrics(texts)
    
    metrics = {
        **distinct_4,
        # "semantic_dispersion": semantic_dispersion,
        **length_metrics
    }
    for k, v in metrics.items():
        if 'distinct' in k.lower():
            metrics[k] = round(v, 4)
        else:
            metrics[k] = round(v, 3)
    print(metrics)
    
    if output_file:
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as writer:
            json.dump(metrics, writer, ensure_ascii=False, indent=4)


if __name__ == '__main__':
    main(input_file_or_dir='output/exp0/model_deepseek-r1-250528---prompt_default_copy1.json', role="user", output_file="user_metrics/exp0")
    main(input_file_or_dir='output/exp1', role="user", output_file="user_metrics/exp1")
    main(input_file_or_dir='output/exp2', role="user", output_file="user_metrics/exp2")
    main(input_file_or_dir='output/exp3', role="user", output_file="user_metrics/exp3")
