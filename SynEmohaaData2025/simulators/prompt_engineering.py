import json
from typing import overload, Literal
from pathlib import Path
from functools import lru_cache


class PromptTemplate:
    """
    template仅包含一个占位符，`persona`。
    注意：chenyuxuan.txt 采用了f-string，不能用format格式化
    """
    def __init__(self, template: str):
        self.template = template
        self.template_type = self.detect_template_type(template)
    
    @staticmethod
    def detect_template_type(template: str) -> Literal['format', 'f-string']:
        """
        检查模板类型
        
        注意：此方法并不是通用方法，而是针对`chenyuxuan.txt`的模板内容设计的判断方法
        """
        if 'persona.get' in template:
            return 'f-string'
        else:
            return 'format'
        
        
    @overload
    def get_prompt(self, *, persona: dict) -> str: ...
    
    def get_prompt(self, **variables) -> str:
        if self.template_type == 'f-string':
            ns = variables.copy()
            expression = 'f"""' + self.template.strip() + '"""'
            prompt = eval(expression, ns)
            return prompt
        elif self.template_type == 'format':
            for k, v in variables.items():
                if isinstance(v, (list, dict, tuple)):
                    variables[k] = json.dumps(v, ensure_ascii=False, indent=4)
            prompt = self.template.format(**variables)
            return prompt
        else:
            raise ValueError(f'Unknown template type: {self.template_type}')


@lru_cache
def load_prompt_templates() -> dict:
    this_dir = Path(__file__).parent
    templates = {}
    for file in (this_dir / 'prompts').glob("*"):
        if not file.is_file():
            continue
        
        if file.suffix == ".txt":
            templates[file.stem] = PromptTemplate(file.read_text(encoding='utf-8'))
        elif file.suffix == ".json":
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, dict):
                prompts = data['prompts']
            elif isinstance(data, list):
                prompts = [item['prompt'] for item in data]
            else:
                raise ValueError(f'Unknown data type: {type(data)}')
            
            for i, prompt in enumerate(prompts, 1):
                templates[f'{file.stem}_{i}'] = PromptTemplate(prompt)
            
    return templates


def check_template(output_file: str):
    templates = load_prompt_templates()
    persona = {
        "Gender": "女生",
        "Age": 13,
        "Occupation": "初中生",
        "Topic": "家庭与亲子",
        "Subtopic": "隐私空间",
        "Situation": "为什么父母总是不尊重我的隐私，这让我感到极度不公和愤怒，但我又不知道怎么说？",
        "Personality": [
            "Closedness",
            "Conscientiousness",
            "Intraversion",
            "Antagonism",
            "Neuroticism"
        ],
        "Event Time": "昨天晚上",
        "Event Location": "我的房间里",
        "Event Participants": "我, 妈妈",
        "Event Description": "从小到大，我妈总是特别喜欢“整理”我的房间，说是帮我保持整洁，但每次都翻得底朝天，我的东西她随便看。她还总说我哥学习好，可以晚睡，而我到点就必须睡觉。我一直觉得这很不公平。昨天晚上，我刚写完日记，还没来得及收好，妈妈就没敲门直接进来了。她看到我桌上的日记本，直接就拿起来翻看，还念出了我写的一些关于同学的隐私话。我当时气得浑身发抖，想吼她，但话到嘴边就变成了一句含糊不清的“你、你就是……别那样”，然后我就僵在那里，看着她，她却只说“你写这些有什么用？小孩子懂什么”，还想给我“建议”。",
        "Emotional Experience Words": [
            "愤怒",
            "无助",
            "被侵犯",
            "屈辱",
            "焦虑",
            "迷茫",
            "压抑",
            "羞愧",
            "烦躁"
        ],
        "Coped Strategies and Effects": "为了能有一个自己的空间，我试过跟我妈说，就是……嗯，别、别进来，但是他们就说我‘不懂事’。我还试过把门反锁，结果被我妈发现后骂了一顿，说要拿我的零花钱。所以我就只能把重要的东西藏起来，比如日记本总是藏在书桌最深处，但藏起来更累，感觉他们还是……反正就是没有用，我根本没法阻止他们。",
        "Goals and Expectations": "我希望能……他们、他们能不那样，就是，能给我自己空间，然后我也能、能说清楚自己的想法，不用每次都这么难受，也不会觉得他们那么不公平。",
        "persona_id": "9ef040d3-228c-48bd-bdb8-b49b8590b05a",
        "Core Drive": "公平守护者-不公愤怒",
        "Reaction Pattern": "迷茫回避与碎片化表达型"
    }
    
    prompts = {}
    for name, template in templates.items():
        prompts[name] = template.get_prompt(persona=persona)
    
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        for k, v in prompts.items():
            print("=" * 10 + k + "=" * 10, file=f)
            print(v, file=f)
            print("\n", file=f)


if __name__ == '__main__':
    check_template('prompts.txt')
