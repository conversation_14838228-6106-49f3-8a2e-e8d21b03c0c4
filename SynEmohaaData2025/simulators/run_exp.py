"""
本次实验的相关设置，指定了一系列模型、prompt模板
"""
import json
import os
import concurrent.futures
from pathlib import Path
from functools import lru_cache
from typing import Generator
from typing_extensions import TypeAlias
from tqdm import tqdm

import gen_dial
from openai_client import OpenAISessionArguments
from utils import OpenAIModelRegistry
from gen_dial import gen_dialogue

from simulators.openai_simulation import OpenAISimulationHelper, PerSimulatorConfig
from simulators.prompt_engineering import load_prompt_templates


@lru_cache
def get_llm_registry():
    this_dir = Path(__file__).parent
    llm_config_file = this_dir.parent / 'llm_configs.json'
    llm_configs = json.loads(
        llm_config_file.read_text(encoding='utf-8')
    )
    return OpenAIModelRegistry(models=llm_configs)
    

# 每个OpenAISimulationHelper采用固定的LLM和system prompt，
# 为了测试不同模型、不同prompt，需要生成多个PerSimulatorConfig

PerSimulatorConfigGenerator: TypeAlias = Generator[PerSimulatorConfig, None, None]

# llm设置
models = ["deepseek-v3-250324", "gpt-4.1", "doubao-seed-1-6-250615", "deepseek-r1-250528","claude-sonnet-4-20250514"]
default_model = "deepseek-r1-250528"

# prompt设置
prompt_templates = load_prompt_templates()
default_prompt_template = prompt_templates['gemini_generated_1']

# persona
persona_file = Path(__file__).parent / 'resources' / 'personas.json'

# 咨询师设置
default_assistant_spec = gen_dial.AssistantSpec(
    name=default_model,
    base_url=get_llm_registry().get_model_spec(default_model).base_url,
    api_key=get_llm_registry().get_model_spec(default_model).api_key,
    backend="openai",
    model=get_llm_registry().get_model_spec(default_model).model,
    system_prompt=gen_dial.counselor_system_prompts['openai'] + '\n回复只能包含语言，可以用语气词或者标点模仿语言的停顿，但不能包含动作、表情、心理活动，回复风格不要受来访者回复的影响'
)

# exp1: 不同模型
exp1 = {
    "exp_name": f"exp1: different models",
    "simulator_configs": [
        PerSimulatorConfig(
            session_arguments=OpenAISessionArguments(
                **get_llm_registry().get_model_spec(model_id).model_dump()
            ),
            system_prompt_template=default_prompt_template,
            name=f"model_{model_id}---prompt_default"
        )
         for model_id in models
    ]
}


# exp2: 不同prompt
exp2 = {
    "exp_name": f"exp2: different prompts",
    "simulator_configs": [
        PerSimulatorConfig(
            session_arguments=OpenAISessionArguments(
                **get_llm_registry().get_model_spec(default_model).model_dump()
            ),
            system_prompt_template=prompt_templates[f'gemini_generated_{i}'],
            name=f"model_{default_model}---prompt_{i}"
        )
        for i in range(1, len(models) + 1)
    ]
}


# exp3: 更高温度、重复生成
# ref: https://api-docs.deepseek.com/zh-cn/quick_start/parameter_settings
exp3 = {
    "exp_name": f"exp3: higher temperature and repetition",
    "simulator_configs": [
        PerSimulatorConfig(
            session_arguments=OpenAISessionArguments(
                **get_llm_registry().get_model_spec(default_model).model_dump(),
                decoding_params={
                    "temperature": 1.5
                },
            ),
            system_prompt_template=default_prompt_template,
            name=f"model_{default_model}---prompt_default---temp_1.5---repetition_{i+1}"
        ) for i in range(len(models))
    ]
}


def in_debug_mode():
    return os.getenv("DEBUG", '0').lower() in ['1', 'true', 't', 'y', 'yes']


def run_exp(exp_id: str, exp: dict, output_dir: Path, n_persona: int, n_thread: int):
    exp_dir = output_dir / exp_id
    
    simulation_helper = OpenAISimulationHelper.load(
        persona_file
    )
    assert n_persona <= simulation_helper.num_user_profile
    
    simulator_configs = exp['simulator_configs']
    assert len({simulator_config.name for simulator_config in simulator_configs}) == len(simulator_configs), "simulator_config names should be unique"
    for simulator_config in tqdm(simulator_configs, desc=f"running simulator_config"):
        if "claude" in simulator_config.session_arguments.model:
            min_thread = 4
        else:
            min_thread = 1024
        
        if in_debug_mode():
            n_persona = 1
        
        with concurrent.futures.ThreadPoolExecutor(min(n_thread, min_thread)) as executor:
            # 提交任务
            futures = {}
            simulator_iterator = simulation_helper.iterate_user_simulator(
                per_simulator_config=simulator_config,
                num_simulator=n_persona
            )
            for index, simulator in enumerate(simulator_iterator):
                assistant = default_assistant_spec.create_assistant_session()
                
                future = executor.submit(
                    gen_dialogue,
                    simulator,
                    assistant,
                    dialogue_index=index
                )
                futures[future] = index
                    
            # 获取结果
            results = [None] * n_persona
            for future in tqdm(
                concurrent.futures.as_completed(futures),
                total=len(futures),
            ):
                index = futures[future]
                if future.exception() is not None:
                    print(f"dialogue {index} failed")
                    print(future.exception())
                else:
                    results[index] = future.result()
        
        # 保存结果
        exp_dir.mkdir(parents=True, exist_ok=True)
        with open(exp_dir / (simulator_config.name + '.json'), 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=4)
            
        if in_debug_mode():
            print("in debug mode, we only generate 1 dialogue")
            return


def main():
    # 指定输出目录
    output_dir = Path(__file__).parent / 'output'
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 依次运行实验
    all_exps = {
        "exp1": exp1,
        "exp2": exp2,
        "exp3": exp3
    }
    
    for exp_id, exp in tqdm(all_exps.items(), desc="Running experiment"):
        run_exp(exp_id, exp, output_dir, n_thread=32, n_persona=10)


if __name__ == "__main__":
    main()
