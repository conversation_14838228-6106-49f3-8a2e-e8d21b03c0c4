import json
import pandas as pd
import numpy as np
from analyze_diff import (
    compute_distinct_n, 
    calculate_semantic_diversity, 
    compute_semantic_dispersion, 
    normalize_field_value
)

# 文件组和模型名称定义
FILE_GROUPS = ["full"]
MODELS = ["doubaoch", "doubao_seed", "ds", "dsr1"]
MODEL_NAMES = {"doubaoch": "Doubao-ch", "doubao_seed": "Doubao-seed", "ds": "ds-v3", "dsr1": "ds-r1"}
FIELDS = ["StrengthsAndResources", "SocialSupportSystem", "FormativeExperiences", "InterestsAndValues"]
FIELDS_TRANSLATION = {
    "StrengthsAndResources": "Strengths And Resources",
    "SocialSupportSystem": "Social Support System",
    "FormativeExperiences": "Formative Experiences",
    "InterestsAndValues": "Interests And Values"
}
METRICS = ["多样性分析", "Distinct-1", "Distinct-2", "Distinct-3", 
           "成对相似度方法\n(值越低表示多样性越高)", 
           "质心距离方法\n(值越高表示多样性越高)"]

def load_json_file(filename):
    """加载JSON文件内容"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"无法加载文件 {filename}: {e}")
        return []

def get_field_texts(personas, field):
    """从人物数据中提取特定字段的所有文本"""
    texts = []
    for persona in personas:
        value = persona.get(field)
        if value:
            normalized_value = normalize_field_value(value)
            if normalized_value.strip():
                texts.append(normalized_value)
    return texts

def analyze_field(texts):
    """分析字段文本的多样性指标"""
    results = {}
    
    # 计算distinct-n
    dist_results = compute_distinct_n(texts, max_n=3)
    results["Distinct-1"] = dist_results["distinct_1"]
    results["Distinct-2"] = dist_results["distinct_2"]
    results["Distinct-3"] = dist_results["distinct_3"]
    
    # 计算成对相似度
    avg_similarity = calculate_semantic_diversity(texts)
    results["成对相似度方法\n(值越低表示多样性越高)"] = avg_similarity if avg_similarity is not None else 1.0
    
    # 计算质心距离
    dispersion_score = compute_semantic_dispersion(texts)
    results["质心距离方法\n(值越高表示多样性越高)"] = dispersion_score if dispersion_score is not None else 0.0
    
    # 添加多样性分析占位行
    results["多样性分析"] = ""
    
    return results

def create_table(results, group):
    """为每组创建表格"""
    # 创建表头行
    header_row = ["prompt选择"]
    for field in FIELDS:
        translated_field = FIELDS_TRANSLATION[field]
        for model in MODELS:
            header_row.append(MODEL_NAMES[model])
        header_row.append("均值")
    
    # 创建数据行
    data_rows = []
    
    # 多样性分析行
    diversity_row = ["多样性分析"]
    for _ in range(len(FIELDS) * (len(MODELS) + 1)):
        diversity_row.append("")
    data_rows.append(diversity_row)
    
    # Distinct-1, Distinct-2, Distinct-3 行
    for n in range(1, 4):
        metric = f"Distinct-{n}"
        row = [metric]
        for field in FIELDS:
            for model in MODELS:
                value = results[field][model][metric]
                row.append(f"{value:.4f}")
            # 计算均值
            values = [results[field][model][metric] for model in MODELS]
            row.append(f"{np.mean(values):.4f}")
        data_rows.append(row)
    
    # 成对相似度方法行
    cosine_row = ["成对相似度方法\n(值越低表示多样性越高)"]
    for field in FIELDS:
        for model in MODELS:
            value = results[field][model]["成对相似度方法\n(值越低表示多样性越高)"]
            cosine_row.append(f"{value:.4f}")
        # 计算均值
        values = [results[field][model]["成对相似度方法\n(值越低表示多样性越高)"] for model in MODELS]
        cosine_row.append(f"{np.mean(values):.4f}")
    data_rows.append(cosine_row)
    
    # 质心距离方法行
    dispersion_row = ["质心距离方法\n(值越高表示多样性越高)"]
    for field in FIELDS:
        for model in MODELS:
            value = results[field][model]["质心距离方法\n(值越高表示多样性越高)"]
            dispersion_row.append(f"{value:.4f}")
        # 计算均值
        values = [results[field][model]["质心距离方法\n(值越高表示多样性越高)"] for model in MODELS]
        dispersion_row.append(f"{np.mean(values):.4f}")
    data_rows.append(dispersion_row)
    
    # 创建DataFrame
    df = pd.DataFrame([header_row] + data_rows)
    
    # 保存表格到CSV和Excel
    df.to_csv(f"{group}_results.csv", header=False, index=False)
    df.to_excel(f"{group}_results.xlsx", header=False, index=False)
    print(f"已保存{group}组的表格到{group}_results.csv和{group}_results.xlsx")
    
    # 打印表格
    print(f"\n{group}组分析结果:")
    print(df.to_string(index=False, header=False))
    
    return df

def create_summary_tables(all_results):
    """创建汇总表格，方便比较不同组的结果"""
    
    # 为每个指标创建一个汇总表格
    metric_names = ["Distinct-1", "Distinct-2", "Distinct-3", 
                    "成对相似度方法\n(值越低表示多样性越高)", 
                    "质心距离方法\n(值越高表示多样性越高)"]
    
    for metric in metric_names:
        # 创建表头
        header_row = ["组别"]
        for field in FIELDS:
            translated_field = FIELDS_TRANSLATION[field]
            for model in MODELS:
                header_row.append(f"{translated_field}-{MODEL_NAMES[model]}")
            header_row.append(f"{translated_field}-均值")
        
        # 创建数据行
        data_rows = []
        for group in FILE_GROUPS:
            row = [group]
            for field in FIELDS:
                for model in MODELS:
                    value = all_results[group][field][model][metric]
                    row.append(f"{value:.4f}")
                # 计算均值
                values = [all_results[group][field][model][metric] for model in MODELS]
                row.append(f"{np.mean(values):.4f}")
            data_rows.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame([header_row] + data_rows)
        
        # 保存表格
        metric_filename = metric.split('\n')[0]  # 去掉可能的换行符
        df.to_csv(f"summary_{metric_filename}.csv", header=False, index=False)
        df.to_excel(f"summary_{metric_filename}.xlsx", header=False, index=False)
        print(f"已保存{metric_filename}汇总表格")
        
        # 打印表格
        print(f"\n{metric}汇总表格:")
        print(df.to_string(index=False, header=False))

def main():
    all_results = {}
    
    # 为每个组分析数据
    for group in FILE_GROUPS:
        print(f"\n正在分析 {group} 组...")
        group_results = {}
        
        for field in FIELDS:
            field_results = {}
            
            for model in MODELS:
                filename = f"expand_{group}_{model}.json"
                print(f"正在处理 {filename}...")
                
                # 加载数据
                personas = load_json_file(filename)
                if not personas:
                    print(f"警告: {filename} 无数据或加载失败")
                    continue
                
                # 提取字段文本
                texts = get_field_texts(personas, field)
                if not texts:
                    print(f"警告: 在 {filename} 中找不到有效的 {field} 字段")
                    continue
                
                print(f"  - 找到 {len(texts)} 个有效的 {field} 值")
                
                # 分析字段
                metrics = analyze_field(texts)
                field_results[model] = metrics
            
            group_results[field] = field_results
        
        # 存储结果
        all_results[group] = group_results
        
        # 创建表格
        create_table(group_results, group)
    
    # 创建汇总表格
    create_summary_tables(all_results)
    
    print("\n分析完成!")

if __name__ == "__main__":
    main()
